/**
 * Guided Journey Page
 *
 * This page demonstrates the guided journey experience with all core components.
 * Enhanced with premium UI/UX and proper error handling.
 */

import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import { useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useGuidedJourney } from '../context/GuidedJourneyContext';
import GuidedJourneyNavigator from '../components/GuidedJourneyNavigator';

import ContextualGuide from '../components/ContextualGuide';
import SeasonalTransition from '../components/SeasonalTransition';
import DirectionsOverview from '../components/DirectionsOverview';
import Button from '../../../components/ui/Button';
import Card, { CardHeader, CardContent, CardActions } from '../../../components/ui/Card';
import { useToast } from '../../../components/ui/ToastProvider';

// Styled Components
const PageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${({ theme }) => theme.colors.background.default};
  transition: background-color 0.3s ease;
`;

const SidebarContainer = styled.div`
  width: 300px;
  padding: ${({ theme }) => theme.spacing(6)};
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-right: 1px solid ${({ theme }) => theme.colors.divider};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  z-index: 1;
  transition:
    background-color 0.3s ease,
    box-shadow 0.3s ease;
`;

const MainContent = styled.div`
  flex: 1;
  padding: ${({ theme }) => theme.spacing(8)};
  overflow-y: auto;
  transition: background-color 0.3s ease;
`;

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing(8)};
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.typography.fontSize.h1};
  font-weight: ${({ theme }) => theme.typography.fontWeight.bold};
  margin-bottom: ${({ theme }) => theme.spacing(2)};
  color: ${({ theme }) => theme.colors.text.primary};
  transition: color 0.3s ease;
`;

const PageDescription = styled.p`
  font-size: ${({ theme }) => theme.typography.fontSize.body};
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: ${({ theme }) => theme.spacing(6)};
  max-width: 800px;
  line-height: 1.6;
  transition: color 0.3s ease;
`;

// Commented out as it's not currently used
// const SectionTitle = styled.h2`
//   font-size: ${({ theme }) => theme.typography.fontSize.h3};
//   font-weight: ${({ theme }) => theme.typography.fontWeight.bold};
//   margin-bottom: ${({ theme }) => theme.spacing(4)};
//   color: ${({ theme }) => theme.colors.text.primary};
//   transition: color 0.3s ease;
// `;

const StepWizardContainer = styled(motion.div)`
  margin-bottom: ${({ theme }) => theme.spacing(8)};
`;

const SeasonalButtonsCard = styled(Card)`
  margin-top: ${({ theme }) => theme.spacing(8)};
`;

const ButtonsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing(4)};
`;

/**
 * Guided Journey Page Component
 */
interface GuidedJourneyPageProps {
  hideSidebar?: boolean;
}

const GuidedJourneyPage: React.FC<GuidedJourneyPageProps> = ({ hideSidebar = false }) => {
  const location = useLocation();
  const {
    currentStep,
    guideMessages,
    // isTransitioning is provided by the context but not used in this component
    currentSeason,
    startSeasonTransition,
    askQuestion,
  } = useGuidedJourney();

  // Check if this component is being rendered inside CompassLayout
  const isInsideCompassLayout = location.pathname.includes('/compass/');

  const [showTransition, setShowTransition] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  // Handle seasonal transition with error handling
  const handleStartTransition = useCallback(
    (season: 'spring' | 'summer' | 'autumn' | 'winter') => {
      try {
        setIsLoading(true);
        setShowTransition(true);
        startSeasonTransition(season);

        // Hide transition after 3 seconds
        setTimeout(() => {
          setShowTransition(false);
          setIsLoading(false);
          showToast(`Transitioned to ${season} season successfully!`, { type: 'success' });
        }, 3000);
      } catch (error) {
        console.error(`Error transitioning to ${season} season:`, error);
        setShowTransition(false);
        setIsLoading(false);
        showToast(`Error transitioning to ${season} season. Please try again.`, { type: 'error' });
      }
    },
    [startSeasonTransition, showToast]
  );

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <PageContainer>
      {/* Sidebar - only show if not inside CompassLayout */}
      {!isInsideCompassLayout && !hideSidebar && (
        <SidebarContainer>
          <GuidedJourneyNavigator currentPath={location.pathname} />
        </SidebarContainer>
      )}

      {/* Main Content */}
      <MainContent style={{ width: isInsideCompassLayout || hideSidebar ? '100%' : 'auto' }}>
        <motion.div variants={containerVariants} initial="hidden" animate="visible">
          <motion.div variants={itemVariants}>
            <PageHeader>
              <PageTitle>{currentStep?.title || 'Guided Journey'}</PageTitle>
              <PageDescription>
                {currentStep?.description ||
                  'Navigate through your life journey with purpose and harmony.'}
              </PageDescription>
            </PageHeader>
          </motion.div>

          {/* Integration Dashboard Banner */}
          <motion.div variants={itemVariants}>
            <IntegrationBanner>
              <BannerContent>
                <BannerIcon>🔗</BannerIcon>
                <BannerText>
                  <BannerTitle>Holistic Integration Dashboard</BannerTitle>
                  <BannerDescription>
                    View your complete life and financial progress in one place
                  </BannerDescription>
                </BannerText>
                <BannerButton onClick={() => (window.location.href = '/integration')}>
                  Open Dashboard
                </BannerButton>
              </BannerContent>
            </IntegrationBanner>
          </motion.div>

          {/* Compass Directions Overview */}
          <motion.div variants={itemVariants}>
            <StepWizardContainer>
              <Card elevation="medium" fullWidth>
                <CardHeader
                  title="Financial Compass Directions"
                  subheader="Explore all aspects of your financial journey"
                />
                <CardContent>
                  <DirectionsOverview />
                </CardContent>
              </Card>
            </StepWizardContainer>
          </motion.div>

          {/* Holistic Integration Dashboard */}
          <motion.div variants={itemVariants}>
            <StepWizardContainer>
              <Card elevation="medium" fullWidth>
                <CardHeader
                  title="Holistic Integration Dashboard"
                  subheader="Comprehensive view of your life and financial journey"
                />
                <CardContent>
                  <p>
                    Get a complete picture of your progress across both your financial compass and
                    life journey. View integrated insights, personalized recommendations, and track
                    your holistic development.
                  </p>
                </CardContent>
                <CardActions align="center">
                  <Button variant="primary" onClick={() => (window.location.href = '/integration')}>
                    🔗 Open Integration Dashboard
                  </Button>
                </CardActions>
              </Card>
            </StepWizardContainer>
          </motion.div>

          {/* Seasonal Transition Buttons */}
          <motion.div variants={itemVariants}>
            <SeasonalButtonsCard elevation="medium" fullWidth>
              <CardHeader
                title="Seasonal Transitions"
                subheader="Experience the different seasons of your journey"
              />
              <CardContent>
                <p>
                  Each season represents a different phase of your life journey. Click on a season
                  to experience the transition.
                </p>
              </CardContent>
              <CardActions align="center">
                <ButtonsContainer>
                  <Button
                    variant="primary"
                    onClick={() => handleStartTransition('spring')}
                    loading={isLoading && showTransition && currentSeason === 'spring'}
                    disabled={isLoading}
                  >
                    Spring Transition
                  </Button>
                  <Button
                    variant="primary"
                    onClick={() => handleStartTransition('summer')}
                    loading={isLoading && showTransition && currentSeason === 'summer'}
                    disabled={isLoading}
                  >
                    Summer Transition
                  </Button>
                  <Button
                    variant="primary"
                    onClick={() => handleStartTransition('autumn')}
                    loading={isLoading && showTransition && currentSeason === 'autumn'}
                    disabled={isLoading}
                  >
                    Autumn Transition
                  </Button>
                  <Button
                    variant="primary"
                    onClick={() => handleStartTransition('winter')}
                    loading={isLoading && showTransition && currentSeason === 'winter'}
                    disabled={isLoading}
                  >
                    Winter Transition
                  </Button>
                </ButtonsContainer>
              </CardActions>
            </SeasonalButtonsCard>
          </motion.div>
        </motion.div>
      </MainContent>

      {/* Contextual Guide */}
      <ContextualGuide
        messages={guideMessages}
        contextKey={currentStep?.id}
        position="bottom-right"
        showAvatar
        onAskQuestion={askQuestion}
      />

      {/* Seasonal Transition */}
      {showTransition && (
        <SeasonalTransition
          currentSeason={currentSeason}
          onTransitionComplete={() => {
            setShowTransition(false);
            setIsLoading(false);
          }}
          duration={3}
          fullScreen
        />
      )}
    </PageContainer>
  );
};

// Integration Banner Styled Components
const IntegrationBanner = styled.div`
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.primary.main},
    ${({ theme }) => theme.colors.primary.light}
  );
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: ${({ theme }) => theme.shadows.md};
  border: 1px solid ${({ theme }) => theme.colors.primary.light};
`;

const BannerContent = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
`;

const BannerIcon = styled.div`
  font-size: 2rem;
  flex-shrink: 0;
`;

const BannerText = styled.div`
  flex: 1;
`;

const BannerTitle = styled.h3`
  color: ${({ theme }) => theme.colors.primary.contrastText};
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 4px 0;
`;

const BannerDescription = styled.p`
  color: ${({ theme }) => theme.colors.primary.contrastText};
  opacity: 0.9;
  margin: 0;
  font-size: 0.95rem;
`;

const BannerButton = styled.button`
  background-color: ${({ theme }) => theme.colors.background.paper};
  color: ${({ theme }) => theme.colors.primary.main};
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background.default};
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }

  &:active {
    transform: translateY(0);
  }

  @media (max-width: 768px) {
    width: 100%;
  }
`;

export default GuidedJourneyPage;

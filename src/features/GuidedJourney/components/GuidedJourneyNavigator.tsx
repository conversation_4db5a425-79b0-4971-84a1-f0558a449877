/**
 * Guided Journey Navigator Component
 *
 * This component provides a visual representation of the user's progress through the journey
 * and allows navigation between different sections.
 */

import React from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useFinancialCompass } from '../../FinancialCompass/context/FinancialCompassContext';
import { useSeasonsOfSelf } from '../../SeasonsOfSelf/context/SeasonsOfSelfContext';

// Types
interface NavigationItem {
  id: string;
  title: string;
  path: string;
  completed: boolean;
  section: 'compass' | 'seasons';
  direction?: 'north' | 'east' | 'south' | 'west';
  season?: 'spring' | 'summer' | 'autumn' | 'winter';
}

interface GuidedJourneyNavigatorProps {
  currentPath: string;
}

// Styled Components
const NavigatorContainer = styled.div`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.md};
  padding: 1.5rem;
  margin-bottom: 2rem;
`;

const NavigatorTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: ${({ theme }) => theme.colors.primary.main};
`;

const SectionHeader = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 500;
`;

const NavigationList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const NavigationSection = styled.div`
  margin-bottom: 1.5rem;
`;

const NavigationItem = styled.li<{ active: boolean; completed: boolean }>`
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: ${({ theme, active }) =>
    active ? theme.colors.primary.light : theme.colors.background.default};
  color: ${({ theme, active }) =>
    active ? theme.colors.primary.contrastText : theme.colors.text.primary};
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme, active }) =>
    active ? theme.colors.primary.light : theme.colors.action.hover};
  }

  ${({ completed, theme }) =>
    completed &&
    `
    &::after {
      content: '✓';
      margin-left: auto;
      color: ${theme.colors.success.main};
      font-weight: bold;
    }
  `}
`;

/**
 * Guided Journey Navigator Component
 */
const GuidedJourneyNavigator: React.FC<GuidedJourneyNavigatorProps> = ({ currentPath }) => {
  const navigate = useNavigate();
  const { subFeatures } = useFinancialCompass();
  const { stages } = useSeasonsOfSelf();

  // Create navigation items from Financial Compass sub-features
  const compassItems: NavigationItem[] = subFeatures.map((subFeature) => ({
    id: subFeature.id,
    title: subFeature.name,
    path: subFeature.path,
    completed: subFeature.completed,
    section: 'compass',
    direction: subFeature.direction,
  }));

  // Create navigation items from Seasons of Self stages
  const seasonsItems: NavigationItem[] = stages.map((stage) => ({
    id: stage.id,
    title: stage.name,
    path: stage.path,
    completed: stage.completed,
    section: 'seasons',
    season: stage.season,
  }));

  // Group compass items by direction
  const northItems = compassItems.filter((item) => item.direction === 'north');
  const eastItems = compassItems.filter((item) => item.direction === 'east');
  const southItems = compassItems.filter((item) => item.direction === 'south');
  const westItems = compassItems.filter((item) => item.direction === 'west');

  // Group seasons items by season
  const springItems = seasonsItems.filter((item) => item.season === 'spring');
  const summerItems = seasonsItems.filter((item) => item.season === 'summer');
  const autumnItems = seasonsItems.filter((item) => item.season === 'autumn');
  const winterItems = seasonsItems.filter((item) => item.season === 'winter');

  // Handle navigation item click
  const handleItemClick = (path: string) => {
    navigate(path);
  };

  return (
    <NavigatorContainer>
      <NavigatorTitle>LifeCompass</NavigatorTitle>
      <NavigationList>
        {/* Home/Overview */}
        <NavigationSection>
          <NavigationItem
            active={currentPath === '/'}
            completed={false}
            onClick={() => handleItemClick('/')}
          >
            Dashboard
          </NavigationItem>
        </NavigationSection>

        {/* Financial Compass Sections */}
        <NavigationSection>
          <SectionHeader>Financial Compass</SectionHeader>

          {/* Main Compass Directions - Simplified Navigation */}
          <NavigationItem
            active={currentPath.includes('/compass/north')}
            completed={northItems.some((item) => item.completed)}
            onClick={() => handleItemClick('/compass/north')}
          >
            North: Where You Are
          </NavigationItem>

          <NavigationItem
            active={currentPath.includes('/compass/east')}
            completed={eastItems.some((item) => item.completed)}
            onClick={() => handleItemClick('/compass/east')}
          >
            East: Where You&apos;re Going
          </NavigationItem>

          <NavigationItem
            active={currentPath.includes('/compass/south')}
            completed={southItems.some((item) => item.completed)}
            onClick={() => handleItemClick('/compass/south')}
          >
            South: What Protects You
          </NavigationItem>

          <NavigationItem
            active={currentPath.includes('/compass/west')}
            completed={westItems.some((item) => item.completed)}
            onClick={() => handleItemClick('/compass/west')}
          >
            West: What You&apos;ll Leave Behind
          </NavigationItem>

          <NavigationItem
            active={currentPath.includes('/compass/summary')}
            completed={
              northItems.some((item) => item.completed) &&
              eastItems.some((item) => item.completed) &&
              southItems.some((item) => item.completed) &&
              westItems.some((item) => item.completed)
            }
            onClick={() => handleItemClick('/compass/summary')}
          >
            Financial Compass Summary
          </NavigationItem>
        </NavigationSection>

        {/* Seasons of Self Sections */}
        <NavigationSection>
          <SectionHeader>Life Journey</SectionHeader>

          {/* Main Seasons - Simplified Navigation */}
          <NavigationItem
            active={currentPath.includes('/seasons/spring')}
            completed={springItems.some((item) => item.completed)}
            onClick={() => handleItemClick('/seasons/spring')}
          >
            Spring: Beginnings
          </NavigationItem>

          <NavigationItem
            active={currentPath.includes('/seasons/summer')}
            completed={summerItems.some((item) => item.completed)}
            onClick={() => handleItemClick('/seasons/summer')}
          >
            Summer: Growth
          </NavigationItem>

          <NavigationItem
            active={currentPath.includes('/seasons/autumn')}
            completed={autumnItems.some((item) => item.completed)}
            onClick={() => handleItemClick('/seasons/autumn')}
          >
            Autumn: Transition
          </NavigationItem>

          <NavigationItem
            active={currentPath.includes('/seasons/winter')}
            completed={winterItems.some((item) => item.completed)}
            onClick={() => handleItemClick('/seasons/winter')}
          >
            Winter: Wisdom
          </NavigationItem>
        </NavigationSection>


      </NavigationList>
    </NavigatorContainer>
  );
};

export default GuidedJourneyNavigator;

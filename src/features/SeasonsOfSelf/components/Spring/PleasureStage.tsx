/**
 * Pleasure Stage Component
 *
 * This component represents the first stage of the Spring season,
 * focusing on discovering what brings immediate joy and pleasure.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface PleasureStageProps {
  onComplete?: () => void;
}

// Activity type
interface PleasureActivity {
  id: string;
  activity: string;
  enjoymentLevel: number;
  frequency: string;
  notes: string;
}

/**
 * Pleasure Stage Component
 */
export const PleasureStage: React.FC<PleasureStageProps> = ({ onComplete }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // Get saved data or initialize with defaults
  const [activities, setActivities] = useState<PleasureActivity[]>(
    data.spring && data.spring.pleasureActivities ? data.spring.pleasureActivities : []
  );

  const [newActivity, setNewActivity] = useState<PleasureActivity>({
    id: '',
    activity: '',
    enjoymentLevel: 0,
    frequency: '',
    notes: '',
  });

  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize auto-save
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'spring-pleasure-stage',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when activities change
  useEffect(() => {
    updateData('spring', 'pleasureActivities', activities);

    // Auto-save data
    autoSave.save(activities);

    // Mark stage as completed if at least 3 activities are added
    const pleasureStage = stages.find((stage) => stage.id === 'pleasure');
    if (pleasureStage && !pleasureStage.completed && activities.length >= 3) {
      updateStageCompletion('pleasure', true);
    } else if (pleasureStage && pleasureStage.completed && activities.length < 3) {
      updateStageCompletion('pleasure', false);
    }
  }, [activities, updateData, stages, autoSave, updateStageCompletion]);

  // Handle adding a new activity
  const handleAddActivity = () => {
    if (!newActivity.activity.trim()) return;

    const activityToAdd = {
      ...newActivity,
      id: Date.now().toString(),
    };

    setActivities([...activities, activityToAdd]);
    setNewActivity({
      id: '',
      activity: '',
      enjoymentLevel: 0,
      frequency: '',
      notes: '',
    });
  };

  // Handle removing an activity
  const handleRemoveActivity = (id: string) => {
    setActivities(activities.filter((activity) => activity.id !== id));
  };

  // Handle input changes for new activity
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setNewActivity({
      ...newActivity,
      [name]: name === 'enjoymentLevel' ? parseInt(value, 10) : value,
    });
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Pleasure Stage</StageTitle>
          <StageDescription>
            The Pleasure Stage is about discovering what brings you immediate joy and sensory
            pleasure. Identify activities that make you feel good in the moment.
          </StageDescription>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Pleasure Activities</SectionTitle>
            <SectionDescription>
              List activities that bring you immediate joy or pleasure. These could be simple things
              like enjoying a cup of coffee, taking a walk, or listening to music.
            </SectionDescription>

            <ActivityForm>
              <FormRow>
                <FormGroup>
                  <Label htmlFor="activity">Activity</Label>
                  <Input
                    id="activity"
                    name="activity"
                    value={newActivity.activity}
                    onChange={handleInputChange}
                    placeholder="e.g., Morning walk in nature"
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="enjoymentLevel">Enjoyment Level (1-10)</Label>
                  <Input
                    id="enjoymentLevel"
                    name="enjoymentLevel"
                    type="number"
                    min="1"
                    max="10"
                    value={newActivity.enjoymentLevel || ''}
                    onChange={handleInputChange}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="frequency">Frequency</Label>
                  <Select
                    id="frequency"
                    name="frequency"
                    value={newActivity.frequency}
                    onChange={handleInputChange}
                  >
                    <option value="">Select frequency</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="rarely">Rarely</option>
                  </Select>
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="notes">Notes</Label>
                  <TextArea
                    id="notes"
                    name="notes"
                    value={newActivity.notes}
                    onChange={handleInputChange}
                    placeholder="Any additional thoughts..."
                  />
                </FormGroup>
              </FormRow>

              <AddButton onClick={handleAddActivity}>Add Activity</AddButton>
            </ActivityForm>

            <ActivitiesList>
              {activities.length === 0 ? (
                <EmptyState>No activities added yet. Add your first activity above.</EmptyState>
              ) : (
                activities.map((activity) => (
                  <ActivityItem key={activity.id}>
                    <ActivityHeader>
                      <ActivityName>{activity.activity}</ActivityName>
                      <ActivityRating>Enjoyment: {activity.enjoymentLevel}/10</ActivityRating>
                      <RemoveButton onClick={() => handleRemoveActivity(activity.id)}>
                        ✕
                      </RemoveButton>
                    </ActivityHeader>
                    <ActivityDetails>
                      <ActivityFrequency>Frequency: {activity.frequency}</ActivityFrequency>
                      {activity.notes && <ActivityNotes>{activity.notes}</ActivityNotes>}
                    </ActivityDetails>
                  </ActivityItem>
                ))
              )}
            </ActivitiesList>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="primary" onClick={handleComplete} disabled={activities.length < 3}>
            {activities.length < 3
              ? `Add ${3 - activities.length} more ${activities.length === 2 ? 'activity' : 'activities'} to continue`
              : 'Continue to Happiness Stage'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const ActivityForm = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const FormRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const FormGroup = styled.div`
  flex: 1;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};
  min-height: 80px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const AddButton = styled.button`
  padding: 8px 16px;
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.action.disabled};
    color: ${({ theme }) => theme.colors.text.disabled};
    cursor: not-allowed;
  }
`;

const ActivitiesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const EmptyState = styled.div`
  padding: 24px;
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  border: 1px dashed ${({ theme }) => theme.colors.border.main};
`;

const ActivityItem = styled.div`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border-left: 3px solid ${({ theme }) => theme.colors.primary.main};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const ActivityHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const ActivityName = styled.h4`
  margin: 0;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const ActivityRating = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.primary.main};
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.error.main};
  cursor: pointer;
  font-size: 1rem;
  padding: 4px;

  &:hover {
    color: ${({ theme }) => theme.colors.error.dark};
  }
`;

const ActivityDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const ActivityFrequency = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const ActivityNotes = styled.p`
  margin: 8px 0 0;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  position: fixed;
  bottom: 24px;
  right: 24px;
  padding: 8px 16px;
  background-color: ${({ theme }) => theme.colors.success.main};
  color: ${({ theme }) => theme.colors.success.contrastText};
  border-radius: 4px;
  box-shadow: ${({ theme }) => theme.shadows.md};
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transform: translateY(${(props) => (props.visible ? 0 : '20px')});
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
  pointer-events: none;
`;

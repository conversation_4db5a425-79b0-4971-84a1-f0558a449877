/**
 * Life Profile Dashboard Component
 *
 * This component provides a comprehensive overview of the user's life profile,
 * integrating data from both Financial Compass and Life Journey (Seasons of Self).
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import { useFinancialCompass } from '../../../FinancialCompass/context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';

interface LifeProfileDashboardProps {
  onViewInsights?: () => void;
  onViewRecommendations?: () => void;
}

interface LifeProfileData {
  // Personal Overview
  name: string;
  age: number;
  currentLifeStage: string;
  lifeStageSeason: string;
  overallProgress: number;

  // Financial Overview
  netWorth: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  savingsRate: number;
  retirementReadiness: number;

  // Life Journey Progress
  springProgress: number;
  summerProgress: number;
  autumnProgress: number;
  winterProgress: number;
  completedStages: number;
  totalStages: number;

  // Integration Insights
  financialLifeAlignment: number;
  priorityAreas: string[];
  nextSteps: string[];
}

const LifeProfileDashboard: React.FC<LifeProfileDashboardProps> = ({
  onViewInsights,
  onViewRecommendations,
}) => {
  const { theme } = useTheme();
  const {
    stages,
    data: lifeData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
  } = useSeasonsOfSelf();
  const { data: financialData, getCompletionPercentage: getFinancialCompletion } =
    useFinancialCompass();

  const [profileData, setProfileData] = useState<LifeProfileData>({
    name: '',
    age: 0,
    currentLifeStage: '',
    lifeStageSeason: '',
    overallProgress: 0,
    netWorth: 0,
    monthlyIncome: 0,
    monthlyExpenses: 0,
    savingsRate: 0,
    retirementReadiness: 0,
    springProgress: 0,
    summerProgress: 0,
    autumnProgress: 0,
    winterProgress: 0,
    completedStages: 0,
    totalStages: 0,
    financialLifeAlignment: 0,
    priorityAreas: [],
    nextSteps: [],
  });

  // Calculate comprehensive life profile data
  useEffect(() => {
    const calculateProfileData = () => {
      // Personal Information
      const personalInfo = financialData.north?.personalInformation || {};
      const name =
        `${personalInfo.firstName || ''} ${personalInfo.lastName || ''}`.trim() || 'User';
      const birthDate = personalInfo.dateOfBirth;
      const age = birthDate ? new Date().getFullYear() - new Date(birthDate).getFullYear() : 0;

      // Current Life Stage
      const currentStage = stages.find((stage) => stage.completed === false) || stages[0];
      const currentLifeStage = currentStage?.name || 'Beginning';
      const lifeStageSeason = currentStage?.season || 'spring';

      // Progress Calculations
      const overallProgress = getCompletionPercentage();
      const springProgress = getSeasonCompletionPercentage('spring');
      const summerProgress = getSeasonCompletionPercentage('summer');
      const autumnProgress = getSeasonCompletionPercentage('autumn');
      const winterProgress = getSeasonCompletionPercentage('winter');
      const completedStages = stages.filter((stage) => stage.completed).length;
      const totalStages = stages.length;

      // Financial Calculations
      const netWorthDetails = financialData.north?.netWorthDetails || {};
      const assets = parseFloat(netWorthDetails.totalAssets || '0');
      const liabilities = parseFloat(netWorthDetails.totalLiabilities || '0');
      const netWorth = assets - liabilities;

      const incomeDetails = financialData.north?.incomeDetails || {};
      const monthlyIncome = parseFloat(incomeDetails.primaryIncome || '0');

      const expenseDetails = financialData.north?.expenseDetails || {};
      const monthlyExpenses = parseFloat(expenseDetails.totalMonthlyExpenses || '0');

      const savingsRate =
        monthlyIncome > 0 ? ((monthlyIncome - monthlyExpenses) / monthlyIncome) * 100 : 0;

      // Retirement Readiness (simplified calculation)
      const retirementReadiness = getFinancialCompletion();

      // Financial-Life Alignment Score
      const financialCompletion = getFinancialCompletion();
      const lifeCompletion = getCompletionPercentage();
      const financialLifeAlignment =
        Math.abs(financialCompletion - lifeCompletion) <= 20
          ? Math.min(financialCompletion, lifeCompletion)
          : (financialCompletion + lifeCompletion) / 2;

      // Priority Areas and Next Steps
      const priorityAreas = [];
      const nextSteps = [];

      if (springProgress < 50) {
        priorityAreas.push('Foundation Building');
        nextSteps.push('Complete pleasure and happiness assessments');
      }
      if (financialCompletion < 30) {
        priorityAreas.push('Financial Planning');
        nextSteps.push('Complete financial assessment');
      }
      if (savingsRate < 10) {
        priorityAreas.push('Savings Optimization');
        nextSteps.push('Review and optimize monthly expenses');
      }

      setProfileData({
        name,
        age,
        currentLifeStage,
        lifeStageSeason,
        overallProgress,
        netWorth,
        monthlyIncome,
        monthlyExpenses,
        savingsRate,
        retirementReadiness,
        springProgress,
        summerProgress,
        autumnProgress,
        winterProgress,
        completedStages,
        totalStages,
        financialLifeAlignment,
        priorityAreas,
        nextSteps,
      });
    };

    calculateProfileData();
  }, [
    stages,
    lifeData,
    financialData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
    getFinancialCompletion,
  ]);

  return (
    <Container theme={theme}>
      <DashboardGrid>
        {/* Enhanced Personal Overview Card */}
        <ProfileCard theme={theme}>
          <CardHeader theme={theme}>
            <CardHeaderContent>
              <CardTitle theme={theme}>Personal Overview</CardTitle>
              <CardIcon>👤</CardIcon>
            </CardHeaderContent>
          </CardHeader>
          <CardContent>
            <ProfileInfo>
              <InfoItem>
                <InfoLabel theme={theme}>Name:</InfoLabel>
                <InfoValue theme={theme}>{profileData.name || 'Not Set'}</InfoValue>
              </InfoItem>
              {profileData.age > 0 && (
                <InfoItem>
                  <InfoLabel theme={theme}>Age:</InfoLabel>
                  <InfoValue theme={theme}>{profileData.age}</InfoValue>
                </InfoItem>
              )}
              <InfoItem>
                <InfoLabel theme={theme}>Current Life Stage:</InfoLabel>
                <InfoValue theme={theme}>
                  {profileData.currentLifeStage || 'Not Determined'}
                </InfoValue>
              </InfoItem>
              <InfoItem>
                <InfoLabel theme={theme}>Season:</InfoLabel>
                <SeasonBadge season={profileData.lifeStageSeason} theme={theme}>
                  {profileData.lifeStageSeason.charAt(0).toUpperCase() +
                    profileData.lifeStageSeason.slice(1)}
                </SeasonBadge>
              </InfoItem>
            </ProfileInfo>
          </CardContent>
        </ProfileCard>

        {/* Enhanced Financial Overview Card */}
        <ProfileCard theme={theme}>
          <CardHeader theme={theme}>
            <CardHeaderContent>
              <CardTitle theme={theme}>Financial Overview</CardTitle>
              <CardIcon>💰</CardIcon>
            </CardHeaderContent>
          </CardHeader>
          <CardContent>
            <FinancialMetrics>
              <MetricItem>
                <MetricIcon>💵</MetricIcon>
                <MetricContent>
                  <MetricValue theme={theme}>
                    {formatCurrency(profileData.monthlyIncome)}
                  </MetricValue>
                  <MetricLabel theme={theme}>Monthly Income</MetricLabel>
                </MetricContent>
              </MetricItem>
              <MetricItem>
                <MetricIcon>💸</MetricIcon>
                <MetricContent>
                  <MetricValue theme={theme}>
                    {formatCurrency(profileData.monthlyExpenses)}
                  </MetricValue>
                  <MetricLabel theme={theme}>Monthly Expenses</MetricLabel>
                </MetricContent>
              </MetricItem>
              <MetricItem>
                <MetricIcon>🏦</MetricIcon>
                <MetricContent>
                  <MetricValue theme={theme}>{formatCurrency(profileData.netWorth)}</MetricValue>
                  <MetricLabel theme={theme}>Net Worth</MetricLabel>
                </MetricContent>
              </MetricItem>
              <MetricItem>
                <MetricIcon>📊</MetricIcon>
                <MetricContent>
                  <MetricValue theme={theme}>{profileData.savingsRate.toFixed(1)}%</MetricValue>
                  <MetricLabel theme={theme}>Savings Rate</MetricLabel>
                </MetricContent>
              </MetricItem>
            </FinancialMetrics>
          </CardContent>
        </ProfileCard>

        {/* Enhanced Life Journey Progress Card - Full Width */}
        <FullWidthProgressCard theme={theme}>
          <CardHeader theme={theme}>
            <CardHeaderContent>
              <CardTitle theme={theme}>Life Journey Progress</CardTitle>
              <CardIcon>🌱</CardIcon>
            </CardHeaderContent>
          </CardHeader>
          <CardContent>
            <SeasonsGridContainer>
              <SeasonsGrid2x2>
                <SeasonProgressCard theme={theme}>
                  <SeasonIcon>🌸</SeasonIcon>
                  <SeasonContent>
                    <SeasonLabel theme={theme}>Spring</SeasonLabel>
                    <SeasonSubtitle theme={theme}>Growth & Renewal</SeasonSubtitle>
                    <ProgressBar theme={theme}>
                      <ProgressFill progress={profileData.springProgress} theme={theme} />
                    </ProgressBar>
                    <ProgressPercent theme={theme}>{profileData.springProgress}%</ProgressPercent>
                  </SeasonContent>
                </SeasonProgressCard>

                <SeasonProgressCard theme={theme}>
                  <SeasonIcon>☀️</SeasonIcon>
                  <SeasonContent>
                    <SeasonLabel theme={theme}>Summer</SeasonLabel>
                    <SeasonSubtitle theme={theme}>Joy & Momentum</SeasonSubtitle>
                    <ProgressBar theme={theme}>
                      <ProgressFill progress={profileData.summerProgress} theme={theme} />
                    </ProgressBar>
                    <ProgressPercent theme={theme}>{profileData.summerProgress}%</ProgressPercent>
                  </SeasonContent>
                </SeasonProgressCard>

                <SeasonProgressCard theme={theme}>
                  <SeasonIcon>🍂</SeasonIcon>
                  <SeasonContent>
                    <SeasonLabel theme={theme}>Autumn</SeasonLabel>
                    <SeasonSubtitle theme={theme}>Goals & Purpose</SeasonSubtitle>
                    <ProgressBar theme={theme}>
                      <ProgressFill progress={profileData.autumnProgress} theme={theme} />
                    </ProgressBar>
                    <ProgressPercent theme={theme}>{profileData.autumnProgress}%</ProgressPercent>
                  </SeasonContent>
                </SeasonProgressCard>

                <SeasonProgressCard theme={theme}>
                  <SeasonIcon>❄️</SeasonIcon>
                  <SeasonContent>
                    <SeasonLabel theme={theme}>Winter</SeasonLabel>
                    <SeasonSubtitle theme={theme}>Calling & Fulfillment</SeasonSubtitle>
                    <ProgressBar theme={theme}>
                      <ProgressFill progress={profileData.winterProgress} theme={theme} />
                    </ProgressBar>
                    <ProgressPercent theme={theme}>{profileData.winterProgress}%</ProgressPercent>
                  </SeasonContent>
                </SeasonProgressCard>
              </SeasonsGrid2x2>
            </SeasonsGridContainer>
          </CardContent>
        </FullWidthProgressCard>

        {/* Action Cards */}
        <ActionCardsContainer>
          <ActionCard onClick={onViewInsights} theme={theme}>
            <ActionCardIcon>🔍</ActionCardIcon>
            <ActionCardContent>
              <ActionCardTitle theme={theme}>View Insights</ActionCardTitle>
              <ActionCardDescription theme={theme}>
                Discover patterns and correlations in your data
              </ActionCardDescription>
            </ActionCardContent>
          </ActionCard>
          <ActionCard onClick={onViewRecommendations} theme={theme}>
            <ActionCardIcon>🎯</ActionCardIcon>
            <ActionCardContent>
              <ActionCardTitle theme={theme}>Get Recommendations</ActionCardTitle>
              <ActionCardDescription theme={theme}>
                Receive personalized action items
              </ActionCardDescription>
            </ActionCardContent>
          </ActionCard>
        </ActionCardsContainer>
      </DashboardGrid>
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.background.default} 0%,
    ${({ theme }) => theme.colors.background.secondary} 100%
  );
  padding: 24px 0;
`;



const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 16px;
  }
`;

const ProfileCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 20px;
  box-shadow: ${({ theme }) => theme.shadows.lg};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }
`;

const FullWidthProgressCard = styled.div`
  grid-column: 1 / -1;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 20px;
  box-shadow: ${({ theme }) => theme.shadows.lg};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }
`;

const CardHeader = styled.div`
  padding: 24px 24px 20px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.background.paper} 0%,
    ${({ theme }) => theme.colors.background.secondary} 100%
  );
`;

const CardHeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const CardTitle = styled.h3`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
`;

const CardIcon = styled.div`
  font-size: 1.5rem;
  opacity: 0.7;
`;

const CardContent = styled.div`
  padding: 24px;
`;

const ProfileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const InfoItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const InfoLabel = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-weight: 500;
`;

const InfoValue = styled.span`
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 600;
`;

const SeasonBadge = styled.span<{ season: string }>`
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 600;
  background-color: ${({ season, theme }) => {
    switch (season) {
      case 'spring':
        return theme.colors.success.light;
      case 'summer':
        return theme.colors.warning.light;
      case 'autumn':
        return theme.colors.error.light;
      case 'winter':
        return theme.colors.info.light;
      default:
        return theme.colors.primary.light;
    }
  }};
  color: ${({ season, theme }) => {
    switch (season) {
      case 'spring':
        return theme.colors.success.dark;
      case 'summer':
        return theme.colors.warning.dark;
      case 'autumn':
        return theme.colors.error.dark;
      case 'winter':
        return theme.colors.info.dark;
      default:
        return theme.colors.primary.dark;
    }
  }};
`;

const FinancialMetrics = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;

const MetricItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background.default};
    transform: translateY(-2px);
  }
`;

const MetricIcon = styled.div`
  font-size: 1.5rem;
  flex-shrink: 0;
`;

const MetricContent = styled.div`
  flex: 1;
`;

const MetricLabel = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  margin-bottom: 4px;
`;

const MetricValue = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.1rem;
  font-weight: 600;
`;

const ProgressOverview = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;





const OverallProgressSection = styled.div`
  text-align: center;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @media (min-width: 768px) {
    min-width: 280px;
    max-width: 280px;
  }
`;

const ProgressRing = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
`;

const ProgressCircle = styled.div<{ progress: number }>`
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: conic-gradient(
    ${({ theme }) => theme.colors.primary.main} ${({ progress }) => progress * 3.6}deg,
    ${({ theme }) => theme.colors.background.secondary} 0deg
  );
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background-color: ${({ theme }) => theme.colors.background.paper};
  }
`;

const ProgressPercentage = styled.div`
  position: relative;
  z-index: 1;
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.5rem;
  font-weight: 700;
`;

const ProgressLabel = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 1.1rem;
`;

const ProgressBar = styled.div<{ small?: boolean }>`
  width: 100%;
  height: ${({ small }) => (small ? '8px' : '12px')};
  background-color: ${({ theme }) => theme.colors.background.secondary};
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const ProgressFill = styled.div<{ progress: number }>`
  width: ${({ progress }) => progress}%;
  height: 100%;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.primary.main},
    ${({ theme }) => theme.colors.primary.light}
  );
  transition: width 0.3s ease;
`;

const ProgressText = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`;

const SeasonProgress = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SeasonsGrid = styled.div`
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const SeasonsGridContainer = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const SeasonsGrid2x2 = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  width: 100%;
  max-width: 600px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 16px;
    max-width: 400px;
  }
`;

const SeasonProgressCard = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.background.secondary} 0%,
    ${({ theme }) => theme.colors.background.default} 100%
  );
  border-radius: 16px;
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  transition: all 0.3s ease;
  text-align: center;
  min-height: 180px;
  aspect-ratio: 1;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.md};
    background: linear-gradient(
      135deg,
      ${({ theme }) => theme.colors.background.default} 0%,
      ${({ theme }) => theme.colors.primary.light}15 100%
    );
  }

  @media (max-width: 768px) {
    padding: 16px;
    min-height: 160px;
    aspect-ratio: auto;
  }
`;

const SeasonProgressItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background.default};
    transform: translateX(4px);
  }
`;

const SeasonIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 12px;

  @media (max-width: 768px) {
    font-size: 1.75rem;
    margin-bottom: 10px;
  }
`;

const SeasonContent = styled.div`
  flex: 1;
  width: 100%;
`;

const SeasonLabel = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 4px;

  @media (max-width: 768px) {
    font-size: 0.9rem;
  }
`;

const SeasonSubtitle = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.75rem;
  margin-bottom: 12px;
  font-style: italic;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 0.7rem;
    margin-bottom: 10px;
  }
`;

const ProgressPercent = styled.div`
  color: ${({ theme }) => theme.colors.primary.main};
  font-size: 1.1rem;
  font-weight: 700;
  margin-top: 8px;

  @media (max-width: 768px) {
    font-size: 1rem;
    margin-top: 6px;
  }
`;

const ActionCardsContainer = styled.div`
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-top: 20px;
  }
`;

const ActionCard = styled.button`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.primary.light} 0%,
    ${({ theme }) => theme.colors.primary.main} 100%
  );
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  color: white;

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }

  @media (max-width: 768px) {
    padding: 16px;
    gap: 12px;
  }
`;

const ActionCardIcon = styled.div`
  font-size: 2rem;
  flex-shrink: 0;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const ActionCardContent = styled.div`
  flex: 1;
`;

const ActionCardTitle = styled.h4`
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 4px 0;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const ActionCardDescription = styled.p`
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.4;

  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
`;

export default LifeProfileDashboard;

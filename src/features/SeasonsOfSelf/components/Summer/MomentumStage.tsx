/**
 * Momentum Stage Component
 *
 * This component represents the second stage of the Summer season,
 * focusing on building momentum through consistent practices and growth.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface MomentumStageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Momentum practice type
interface MomentumPractice {
  id: string;
  practice: string;
  category: string;
  consistency: string;
  growthPotential: number;
  obstacles: string;
  strategies: string;
}

// Practice categories
const practiceCategories = [
  { id: 'physical', name: 'Physical', description: 'Exercise, nutrition, sleep, etc.' },
  { id: 'mental', name: 'Mental', description: 'Learning, skill development, etc.' },
  { id: 'emotional', name: 'Emotional', description: 'Emotional regulation, resilience, etc.' },
  { id: 'social', name: 'Social', description: 'Relationships, community, etc.' },
  { id: 'spiritual', name: 'Spiritual', description: 'Meaning, purpose, values, etc.' },
  { id: 'creative', name: 'Creative', description: 'Artistic expression, innovation, etc.' },
];

/**
 * Momentum Stage Component
 */
export const MomentumStage: React.FC<MomentumStageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // Get saved data or initialize with defaults
  const [practices, setPractices] = useState<MomentumPractice[]>(
    data.summer && data.summer.momentumPractices ? data.summer.momentumPractices : []
  );

  const [newPractice, setNewPractice] = useState<MomentumPractice>({
    id: '',
    practice: '',
    category: '',
    consistency: '',
    growthPotential: 0,
    obstacles: '',
    strategies: '',
  });

  // Save indicator state
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Create auto-save instance
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'summer_momentum_practices',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when practices change
  useEffect(() => {
    updateData('summer', 'momentumPractices', practices);

    // Auto-save data
    autoSave.save(practices);

    // Mark stage as completed if at least 3 practices are added
    const momentumStage = stages.find((stage) => stage.id === 'momentum');
    if (momentumStage && !momentumStage.completed && practices.length >= 3) {
      updateStageCompletion('momentum', true);
    } else if (momentumStage && momentumStage.completed && practices.length < 3) {
      updateStageCompletion('momentum', false);
    }
  }, [practices, updateData, stages, autoSave, updateStageCompletion]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setNewPractice((prev) => ({
      ...prev,
      [name]: name === 'growthPotential' ? parseInt(value) || 0 : value,
    }));
  };

  // Handle adding a new practice
  const handleAddPractice = () => {
    if (!newPractice.practice || !newPractice.category) return;

    const newItem = {
      ...newPractice,
      id: `momentum-${Date.now()}`,
    };

    setPractices((prev) => [...prev, newItem]);
    setNewPractice({
      id: '',
      practice: '',
      category: '',
      consistency: '',
      growthPotential: 0,
      obstacles: '',
      strategies: '',
    });
  };

  // Handle removing a practice
  const handleRemovePractice = (id: string) => {
    setPractices((prev) => prev.filter((practice) => practice.id !== id));
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Momentum Stage</StageTitle>
          <StageDescription>
            The Momentum Stage is about building consistent practices that create positive growth
            and forward movement in your life. Identify practices that help you maintain momentum
            and overcome obstacles.
          </StageDescription>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Momentum Practices</SectionTitle>
            <SectionDescription>
              List practices that help you maintain momentum and growth in different areas of your
              life. These could include daily habits, routines, or disciplines that keep you moving
              forward.
            </SectionDescription>

            <CategoriesGrid>
              {practiceCategories.map((category) => (
                <CategoryCard key={category.id}>
                  <CategoryName>{category.name}</CategoryName>
                  <CategoryDescription>{category.description}</CategoryDescription>
                </CategoryCard>
              ))}
            </CategoriesGrid>

            <PracticeForm>
              <FormRow>
                <FormGroup>
                  <Label htmlFor="practice">Momentum Practice</Label>
                  <Input
                    id="practice"
                    name="practice"
                    value={newPractice.practice}
                    onChange={handleInputChange}
                    placeholder="e.g., Daily morning exercise routine"
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    id="category"
                    name="category"
                    value={newPractice.category}
                    onChange={handleInputChange}
                  >
                    <option value="">Select category</option>
                    {practiceCategories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </Select>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="consistency">Current Consistency</Label>
                  <Select
                    id="consistency"
                    name="consistency"
                    value={newPractice.consistency}
                    onChange={handleInputChange}
                  >
                    <option value="">Select consistency</option>
                    <option value="daily">Daily</option>
                    <option value="several_times_a_week">Several times a week</option>
                    <option value="weekly">Weekly</option>
                    <option value="occasionally">Occasionally</option>
                    <option value="just_starting">Just starting</option>
                  </Select>
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="growthPotential">Growth Potential (1-10)</Label>
                  <Input
                    id="growthPotential"
                    name="growthPotential"
                    type="number"
                    min="1"
                    max="10"
                    value={newPractice.growthPotential || ''}
                    onChange={handleInputChange}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="obstacles">Obstacles</Label>
                  <TextArea
                    id="obstacles"
                    name="obstacles"
                    value={newPractice.obstacles}
                    onChange={handleInputChange}
                    placeholder="What obstacles might prevent you from maintaining this practice?"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="strategies">Strategies</Label>
                  <TextArea
                    id="strategies"
                    name="strategies"
                    value={newPractice.strategies}
                    onChange={handleInputChange}
                    placeholder="What strategies will help you maintain consistency with this practice?"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <AddButton
                onClick={handleAddPractice}
                disabled={!newPractice.practice || !newPractice.category}
              >
                Add Momentum Practice
              </AddButton>
            </PracticeForm>

            <PracticesList>
              {practices.length === 0 ? (
                <EmptyState>
                  No momentum practices added yet. Add your first practice above.
                </EmptyState>
              ) : (
                practices.map((practice) => (
                  <PracticeItem key={practice.id}>
                    <PracticeHeader>
                      <PracticeName>{practice.practice}</PracticeName>
                      <PracticeCategory>
                        {practiceCategories.find((c) => c.id === practice.category)?.name ||
                          practice.category}
                      </PracticeCategory>
                      <RemoveButton onClick={() => handleRemovePractice(practice.id)}>
                        ✕
                      </RemoveButton>
                    </PracticeHeader>
                    <PracticeDetails>
                      <PracticeConsistency>
                        Consistency: {practice.consistency.replace('_', ' ')}
                      </PracticeConsistency>
                      <PracticeGrowth>
                        Growth Potential: {practice.growthPotential}/10
                      </PracticeGrowth>
                      {practice.obstacles && (
                        <PracticeObstacles>
                          <ObstaclesLabel>Obstacles:</ObstaclesLabel> {practice.obstacles}
                        </PracticeObstacles>
                      )}
                      {practice.strategies && (
                        <PracticeStrategies>
                          <StrategiesLabel>Strategies:</StrategiesLabel> {practice.strategies}
                        </PracticeStrategies>
                      )}
                    </PracticeDetails>
                  </PracticeItem>
                ))
              )}
            </PracticesList>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="outlined" onClick={handleBack}>
            Back to Joy Stage
          </Button>
          <Button variant="primary" onClick={handleComplete} disabled={practices.length < 3}>
            {practices.length < 3
              ? `Add ${3 - practices.length} more ${practices.length === 2 ? 'practice' : 'practices'} to continue`
              : 'Complete Summer Season'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const CategoriesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const CategoryCard = styled.div`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const CategoryName = styled.h4`
  margin: 0 0 8px;
  color: ${({ theme }) => theme.colors.primary.main};
`;

const CategoryDescription = styled.p`
  margin: 0;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const PracticeForm = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const FormRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 600px) {
    flex-direction: column;
  }
`;

const FormGroup = styled.div<{ fullWidth?: boolean }>`
  flex: ${(props) => (props.fullWidth ? 1 : 0.5)};

  @media (max-width: 600px) {
    flex: 1;
  }
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const AddButton = styled(Button)`
  margin-top: 8px;
`;

const PracticesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const PracticeItem = styled.div`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border-left: 4px solid ${({ theme }) => theme.colors.primary.main};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const PracticeHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const PracticeName = styled.h4`
  margin: 0;
  flex: 1;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const PracticeCategory = styled.span`
  margin-right: 16px;
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  border-radius: 4px;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.primary.main};
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  font-size: 1rem;
  padding: 4px;

  &:hover {
    color: ${({ theme }) => theme.colors.error.main};
  }
`;

const PracticeDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const PracticeConsistency = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const PracticeGrowth = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const PracticeObstacles = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const ObstaclesLabel = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const PracticeStrategies = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const StrategiesLabel = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const EmptyState = styled.div`
  padding: 24px;
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  border: 1px dashed ${({ theme }) => theme.colors.border.main};
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  text-align: center;
  color: ${({ theme }) => theme.colors.success.main};
  margin-top: 16px;
`;

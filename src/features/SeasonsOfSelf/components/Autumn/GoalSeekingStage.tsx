/**
 * Goal Seeking Stage Component
 *
 * This component represents the second stage of the Autumn season,
 * focusing on setting and pursuing meaningful goals.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';
import { LifeDesignCanvas } from './LifeDesignCanvas';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface GoalSeekingStageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Goal type
interface Goal {
  id: string;
  title: string;
  category: string;
  timeframe: string;
  description: string;
  motivation: string;
  milestones: string[];
  obstacles: string;
  resources: string;
}

// Goal categories
const goalCategories = [
  { id: 'career', name: 'Career & Work' },
  { id: 'relationships', name: 'Relationships' },
  { id: 'health', name: 'Health & Wellbeing' },
  { id: 'finances', name: 'Finances' },
  { id: 'personal_growth', name: 'Personal Growth' },
  { id: 'spirituality', name: 'Spirituality & Purpose' },
  { id: 'lifestyle', name: 'Lifestyle & Environment' },
  { id: 'community', name: 'Community & Contribution' },
];

// Timeframe options
const timeframeOptions = [
  { id: 'short_term', name: 'Short-term (< 1 year)' },
  { id: 'medium_term', name: 'Medium-term (1-3 years)' },
  { id: 'long_term', name: 'Long-term (3+ years)' },
];

/**
 * Goal Seeking Stage Component
 */
export const GoalSeekingStage: React.FC<GoalSeekingStageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // State for choosing assessment method
  const [assessmentMethod, setAssessmentMethod] = useState<'choose' | 'traditional' | 'lifedesign'>(
    'choose'
  );

  // Get saved data or initialize with defaults
  const [goals, setGoals] = useState<Goal[]>(
    data.autumn && data.autumn.goals ? data.autumn.goals : []
  );

  const [newGoal, setNewGoal] = useState<Goal>({
    id: '',
    title: '',
    category: '',
    timeframe: '',
    description: '',
    motivation: '',
    milestones: [],
    obstacles: '',
    resources: '',
  });

  const [newMilestone, setNewMilestone] = useState('');

  // Save indicator state
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Create auto-save instance
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'autumn_goals',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when goals change
  useEffect(() => {
    updateData('autumn', 'goals', goals);

    // Auto-save data
    autoSave.save(goals);

    // Mark stage as completed if at least 2 goals are added
    const goalSeekingStage = stages.find((stage) => stage.id === 'goal_seeking');
    if (goalSeekingStage && !goalSeekingStage.completed && goals.length >= 2) {
      updateStageCompletion('goal_seeking', true);
    } else if (goalSeekingStage && goalSeekingStage.completed && goals.length < 2) {
      updateStageCompletion('goal_seeking', false);
    }
  }, [goals, updateData, stages, autoSave, updateStageCompletion]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setNewGoal((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle milestone input change
  const handleMilestoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMilestone(e.target.value);
  };

  // Handle adding a milestone
  const handleAddMilestone = () => {
    if (!newMilestone.trim()) return;

    setNewGoal((prev) => ({
      ...prev,
      milestones: [...prev.milestones, newMilestone.trim()],
    }));

    setNewMilestone('');
  };

  // Handle removing a milestone
  const handleRemoveMilestone = (index: number) => {
    setNewGoal((prev) => ({
      ...prev,
      milestones: prev.milestones.filter((_, i) => i !== index),
    }));
  };

  // Handle adding a new goal
  const handleAddGoal = () => {
    if (!newGoal.title || !newGoal.category || !newGoal.timeframe) return;

    const newItem = {
      ...newGoal,
      id: `goal-${Date.now()}`,
    };

    setGoals((prev) => [...prev, newItem]);
    setNewGoal({
      id: '',
      title: '',
      category: '',
      timeframe: '',
      description: '',
      motivation: '',
      milestones: [],
      obstacles: '',
      resources: '',
    });
  };

  // Handle removing a goal
  const handleRemoveGoal = (id: string) => {
    setGoals((prev) => prev.filter((goal) => goal.id !== id));
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  // Check if user has existing data to determine initial method
  useEffect(() => {
    if (data.autumn?.lifeDesignData) {
      setAssessmentMethod('lifedesign');
    } else if (data.autumn?.goals && data.autumn.goals.length > 0) {
      setAssessmentMethod('traditional');
    }
  }, [data.autumn]);

  // Render method selection
  if (assessmentMethod === 'choose') {
    return (
      <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
        <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
          <CardHeader>
            <StageTitle>Goal Seeking Stage</StageTitle>
            <StageDescription>
              Choose your preferred method to design and pursue meaningful goals.
            </StageDescription>
          </CardHeader>

          <CardContent>
            <MethodSelection>
              <MethodOption onClick={() => setAssessmentMethod('lifedesign')}>
                <MethodTitle>🎨 Life Design Canvas</MethodTitle>
                <MethodDescription>
                  Use design thinking methodology to prototype your ideal life, create experiments,
                  and iterate on major decisions.
                </MethodDescription>
                <MethodBadge>Recommended</MethodBadge>
              </MethodOption>

              <MethodOption onClick={() => setAssessmentMethod('traditional')}>
                <MethodTitle>🎯 Traditional Goal Setting</MethodTitle>
                <MethodDescription>
                  Set specific, meaningful goals with clear milestones, timelines, and action plans.
                </MethodDescription>
              </MethodOption>
            </MethodSelection>
          </CardContent>
        </StageCard>
      </Container>
    );
  }

  // Render Life Design Canvas
  if (assessmentMethod === 'lifedesign') {
    return (
      <LifeDesignCanvas
        onComplete={onComplete}
        onBack={() => setAssessmentMethod('choose')}
        initialData={data.autumn?.lifeDesignData}
      />
    );
  }

  // Render traditional goal setting
  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Goal Seeking Stage</StageTitle>
          <StageDescription>
            The Goal Seeking Stage is about setting and pursuing meaningful goals that align with
            your values and purpose. Define clear goals and create actionable plans to achieve them.
          </StageDescription>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Meaningful Goals</SectionTitle>
            <SectionDescription>
              Define goals that are meaningful to you and aligned with your values. Create a clear
              plan with milestones to track your progress.
            </SectionDescription>

            <GoalForm>
              <FormRow>
                <FormGroup>
                  <Label htmlFor="title">Goal Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={newGoal.title}
                    onChange={handleInputChange}
                    placeholder="e.g., Start my own business"
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    id="category"
                    name="category"
                    value={newGoal.category}
                    onChange={handleInputChange}
                  >
                    <option value="">Select category</option>
                    {goalCategories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </Select>
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="timeframe">Timeframe</Label>
                  <Select
                    id="timeframe"
                    name="timeframe"
                    value={newGoal.timeframe}
                    onChange={handleInputChange}
                  >
                    <option value="">Select timeframe</option>
                    {timeframeOptions.map((option) => (
                      <option key={option.id} value={option.id}>
                        {option.name}
                      </option>
                    ))}
                  </Select>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="description">Description</Label>
                  <TextArea
                    id="description"
                    name="description"
                    value={newGoal.description}
                    onChange={handleInputChange}
                    placeholder="Describe your goal in detail"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="motivation">Motivation</Label>
                  <TextArea
                    id="motivation"
                    name="motivation"
                    value={newGoal.motivation}
                    onChange={handleInputChange}
                    placeholder="Why is this goal important to you?"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label>Milestones</Label>
                  <MilestoneInputGroup>
                    <MilestoneInput
                      value={newMilestone}
                      onChange={handleMilestoneChange}
                      placeholder="Add a milestone"
                    />
                    <MilestoneButton onClick={handleAddMilestone}>Add</MilestoneButton>
                  </MilestoneInputGroup>

                  <MilestonesList>
                    {newGoal.milestones.map((milestone, index) => (
                      <MilestoneItem key={index}>
                        <MilestoneText>{milestone}</MilestoneText>
                        <MilestoneRemove onClick={() => handleRemoveMilestone(index)}>
                          ✕
                        </MilestoneRemove>
                      </MilestoneItem>
                    ))}
                  </MilestonesList>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="obstacles">Potential Obstacles</Label>
                  <TextArea
                    id="obstacles"
                    name="obstacles"
                    value={newGoal.obstacles}
                    onChange={handleInputChange}
                    placeholder="What obstacles might you face?"
                    rows={2}
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="resources">Resources Needed</Label>
                  <TextArea
                    id="resources"
                    name="resources"
                    value={newGoal.resources}
                    onChange={handleInputChange}
                    placeholder="What resources will you need?"
                    rows={2}
                  />
                </FormGroup>
              </FormRow>

              <AddButton
                onClick={handleAddGoal}
                disabled={!newGoal.title || !newGoal.category || !newGoal.timeframe}
              >
                Add Goal
              </AddButton>
            </GoalForm>

            <GoalsList>
              {goals.length === 0 ? (
                <EmptyState>No goals added yet. Add your first goal above.</EmptyState>
              ) : (
                goals.map((goal) => (
                  <GoalItem key={goal.id}>
                    <GoalHeader>
                      <GoalTitle>{goal.title}</GoalTitle>
                      <GoalMeta>
                        <CategoryBadge>
                          {goalCategories.find((c) => c.id === goal.category)?.name ||
                            goal.category}
                        </CategoryBadge>
                        <TimeframeBadge>
                          {timeframeOptions.find((t) => t.id === goal.timeframe)?.name ||
                            goal.timeframe}
                        </TimeframeBadge>
                      </GoalMeta>
                      <RemoveButton onClick={() => handleRemoveGoal(goal.id)}>✕</RemoveButton>
                    </GoalHeader>

                    <GoalDetails>
                      {goal.description && <GoalDescription>{goal.description}</GoalDescription>}

                      {goal.motivation && (
                        <GoalMotivation>
                          <SectionLabel>Motivation:</SectionLabel> {goal.motivation}
                        </GoalMotivation>
                      )}

                      {goal.milestones.length > 0 && (
                        <GoalMilestones>
                          <SectionLabel>Milestones:</SectionLabel>
                          <MilestonesListView>
                            {goal.milestones.map((milestone, index) => (
                              <MilestoneListItem key={index}>{milestone}</MilestoneListItem>
                            ))}
                          </MilestonesListView>
                        </GoalMilestones>
                      )}

                      {goal.obstacles && (
                        <GoalObstacles>
                          <SectionLabel>Obstacles:</SectionLabel> {goal.obstacles}
                        </GoalObstacles>
                      )}

                      {goal.resources && (
                        <GoalResources>
                          <SectionLabel>Resources:</SectionLabel> {goal.resources}
                        </GoalResources>
                      )}
                    </GoalDetails>
                  </GoalItem>
                ))
              )}
            </GoalsList>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="outlined" onClick={handleBack}>
            Back to Pivot Stage
          </Button>
          <Button variant="primary" onClick={handleComplete} disabled={goals.length < 2}>
            {goals.length < 2
              ? `Add ${2 - goals.length} more ${goals.length === 1 ? 'goal' : 'goals'} to continue`
              : 'Complete Autumn Season'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const GoalForm = styled.div`
margin - bottom: 24px;
padding: 16px;
background - color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
border - radius: 8px;
box - shadow: ${({ theme }) => theme.shadows.sm};
`;

const FormRow = styled.div`
display: flex;
gap: 16px;
margin - bottom: 16px;

@media(max - width: 600px) {
  flex - direction: column;
}
`;

const FormGroup = styled.div<{ fullWidth?: boolean }>`
  flex: ${(props) => (props.fullWidth ? 1 : 0.5)};

  @media (max - width: 600px) {
    flex: 1;
  }
`;

const Label = styled.label`
display: block;
margin - bottom: 8px;
font - weight: 500;
color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const Input = styled.input`
width: 100 %;
padding: 8px 12px;
border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
border - radius: 4px;
font - size: 1rem;

  &:focus {
  outline: none;
  border - color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  box - shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
}
`;

const Select = styled.select`
width: 100 %;
padding: 8px 12px;
border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
border - radius: 4px;
font - size: 1rem;
background - color: ${({ theme }) => theme.colors?.paper || '#ffffff'};

  &:focus {
  outline: none;
  border - color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  box - shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
}
`;

const TextArea = styled.textarea`
width: 100 %;
padding: 8px 12px;
border: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
border - radius: 4px;
font - size: 1rem;
resize: vertical;

  &:focus {
  outline: none;
  border - color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  box - shadow: 0 0 0 2px ${({ theme }) => `${theme.colors?.primary || '#1976d2'}40`};
}
`;

const MilestoneInputGroup = styled.div`
display: flex;
gap: 8px;
margin - bottom: 8px;
`;

const MilestoneInput = styled(Input)`
  flex: 1;
`;

const MilestoneButton = styled(Button)`
  padding: 8px 16px;
`;

const MilestonesList = styled.div`
display: flex;
flex - direction: column;
gap: 8px;
`;

const MilestoneItem = styled.div`
display: flex;
align - items: center;
padding: 8px 12px;
background - color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
border - radius: 4px;
`;

const MilestoneText = styled.span`
flex: 1;
font - size: 0.875rem;
`;

const MilestoneRemove = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  cursor: pointer;

  &:hover {
    color: ${({ theme }) => theme.colors?.error || '#f44336'};
  }
`;

const AddButton = styled(Button)`
margin - top: 8px;
`;

const GoalsList = styled.div`
display: flex;
flex - direction: column;
gap: 16px;
`;

const GoalItem = styled.div`
padding: 16px;
background - color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
border - radius: 8px;
box - shadow: ${({ theme }) => theme.shadows.sm};
border - left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const GoalHeader = styled.div`
display: flex;
align - items: center;
margin - bottom: 12px;
`;

const GoalTitle = styled.h4`
  margin: 0;
  flex: 1;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const GoalMeta = styled.div`
display: flex;
gap: 8px;
margin - right: 16px;
`;

const CategoryBadge = styled.span`
padding: 4px 8px;
background - color: ${({ theme }) =>
  theme.mode === 'dark'
    ? `${theme.colors?.background || '#ffffff'}CC` // Use dark background with opacity in dark mode
    : theme.colors?.background || '#ffffff'};
border - radius: 4px;
font - size: 0.75rem;
color: ${({ theme }) =>
  theme.mode === 'dark'
    ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
    : theme.colors?.primary || '#1976d2'};
`;

const TimeframeBadge = styled.span`
padding: 4px 8px;
background - color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
border - radius: 4px;
font - size: 0.75rem;
color: ${({ theme }) =>
  theme.mode === 'dark'
    ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
    : theme.colors?.text || '#000000'};
`;

const RemoveButton = styled.button`
background: none;
border: none;
color: ${({ theme }) => theme.colors?.text || '#000000'};
cursor: pointer;
font - size: 1rem;
padding: 4px;

  &:hover {
  color: ${({ theme }) => theme.colors?.error || '#f44336'};
}
`;

const GoalDetails = styled.div`
display: flex;
flex - direction: column;
gap: 12px;
`;

const GoalDescription = styled.p`
  margin: 0;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const GoalMotivation = styled.div`
font - size: 0.875rem;
color: ${({ theme }) =>
  theme.mode === 'dark'
    ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
    : theme.colors?.text || '#000000'};
`;

const GoalMilestones = styled.div`
font - size: 0.875rem;
`;

const MilestonesListView = styled.ul`
margin: 8px 0 0;
padding - left: 20px;
`;

const MilestoneListItem = styled.li`
margin - bottom: 4px;
`;

const GoalObstacles = styled.div`
font - size: 0.875rem;
color: ${({ theme }) =>
  theme.mode === 'dark'
    ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
    : theme.colors?.text || '#000000'};
`;

const GoalResources = styled.div`
font - size: 0.875rem;
color: ${({ theme }) =>
  theme.mode === 'dark'
    ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
    : theme.colors?.text || '#000000'};
`;

const SectionLabel = styled.span`
font - weight: 500;
color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const EmptyState = styled.div`
padding: 24px;
text - align: center;
color: ${({ theme }) =>
  theme.mode === 'dark'
    ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
    : theme.colors?.text || '#000000'};
background - color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
border - radius: 8px;
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
opacity: ${(props) => (props.visible ? 1 : 0)};
transition: opacity 0.3s ease;
text - align: center;
color: ${({ theme }) => theme.colors?.success || '#4caf50'};
margin - top: 16px;
`;

// Method selection styles
const MethodSelection = styled.div`
display: grid;
grid - template - columns: 1fr 1fr;
gap: 20px;
margin: 24px 0;
`;

const MethodOption = styled.div`
padding: 20px;
border: 2px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
border - radius: 8px;
cursor: pointer;
transition: all 0.3s ease;

  &:hover {
  border - color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  background - color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}10`};
}
`;

const MethodTitle = styled.h3`
color: ${({ theme }) => theme.colors?.text || '#000000'};
margin - bottom: 8px;
font - size: 1.1rem;
`;

const MethodDescription = styled.p`
color: ${({ theme }) => theme.colors?.text || '#000000'};
font - size: 0.9rem;
line - height: 1.4;
margin - bottom: 8px;
`;

const MethodBadge = styled.span`
display: inline - block;
padding: 4px 8px;
background - color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
color: white;
border - radius: 12px;
font - size: 0.75rem;
font - weight: 600;
`;

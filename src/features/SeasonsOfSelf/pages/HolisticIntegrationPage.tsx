/**
 * Holistic Integration Page
 *
 * This page provides a comprehensive integration dashboard that combines
 * Life Profile Dashboard, Integrated Insights, and Personalized Recommendations.
 * Enhanced with professional UI/UX design, animations, and modern layout.
 */

import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import LifeProfileDashboard from '../components/integration/LifeProfileDashboard';
import IntegratedInsights from '../components/integration/IntegratedInsights';
import PersonalizedRecommendations from '../components/integration/PersonalizedRecommendations';

type ActiveView = 'dashboard' | 'insights' | 'recommendations';

const HolisticIntegrationPage: React.FC = () => {
  const { theme } = useTheme();
  const [activeView, setActiveView] = useState<ActiveView>('dashboard');
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    // Simulate loading for smooth transition
    const timer = setTimeout(() => setIsLoading(false), 800);
    return () => clearTimeout(timer);
  }, []);

  const handleViewChange = (view: ActiveView) => {
    if (view === activeView) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setActiveView(view);
      setIsTransitioning(false);
    }, 150);
  };

  const handleViewInsights = () => {
    handleViewChange('insights');
  };

  const handleViewRecommendations = () => {
    handleViewChange('recommendations');
  };

  const handleTakeAction = (actionId: string) => {
    // Handle action based on actionId
    console.log('Taking action:', actionId);

    // Navigate to appropriate section based on action
    if (actionId.includes('personal-info') || actionId.includes('financial')) {
      // Navigate to Financial Compass
      window.location.href = '/financial-compass';
    } else if (actionId.includes('spring') || actionId.includes('life')) {
      // Navigate to Life Journey
      window.location.href = '/seasons';
    }
  };

  const getViewTitle = () => {
    switch (activeView) {
      case 'insights':
        return 'Integrated Insights';
      case 'recommendations':
        return 'Personalized Recommendations';
      default:
        return 'Life Profile Dashboard';
    }
  };

  const getViewDescription = () => {
    switch (activeView) {
      case 'insights':
        return 'Discover patterns and correlations across your financial and life journey';
      case 'recommendations':
        return 'Get personalized action items tailored to your current situation';
      default:
        return 'Your comprehensive life and financial overview';
    }
  };

  const renderActiveView = () => {
    switch (activeView) {
      case 'insights':
        return <IntegratedInsights onViewRecommendations={handleViewRecommendations} />;
      case 'recommendations':
        return <PersonalizedRecommendations onTakeAction={handleTakeAction} />;
      default:
        return (
          <LifeProfileDashboard
            onViewInsights={handleViewInsights}
            onViewRecommendations={handleViewRecommendations}
          />
        );
    }
  };

  if (isLoading) {
    return (
      <LoadingContainer theme={theme}>
        <LoadingSpinner />
        <LoadingText theme={theme}>Loading your holistic dashboard...</LoadingText>
      </LoadingContainer>
    );
  }

  return (
    <Container theme={theme}>
      {/* Hero Header */}
      <HeroHeader theme={theme}>
        <HeroBackground />
        <HeroContent>
          <HeroTitle theme={theme}>LifeCompass Dashboard</HeroTitle>
          <HeroSubtitle theme={theme}>
            Your complete life and financial journey in one place
          </HeroSubtitle>
          <BreadcrumbNav theme={theme}>
            <BreadcrumbItem>Home</BreadcrumbItem>
            <BreadcrumbSeparator>›</BreadcrumbSeparator>
            <BreadcrumbItem active>{getViewTitle()}</BreadcrumbItem>
          </BreadcrumbNav>
        </HeroContent>
      </HeroHeader>

      {/* Enhanced Navigation */}
      <NavigationSection theme={theme}>
        <NavigationContainer>
          <ViewInfo>
            <ViewTitle theme={theme}>{getViewTitle()}</ViewTitle>
            <ViewDescription theme={theme}>{getViewDescription()}</ViewDescription>
          </ViewInfo>
          <NavigationTabs>
            <NavigationTab
              active={activeView === 'dashboard'}
              onClick={() => handleViewChange('dashboard')}
              theme={theme}
            >
              <TabIcon>📊</TabIcon>
              <TabLabel>Life Profile</TabLabel>
              <TabIndicator active={activeView === 'dashboard'} theme={theme} />
            </NavigationTab>
            <NavigationTab
              active={activeView === 'insights'}
              onClick={() => handleViewChange('insights')}
              theme={theme}
            >
              <TabIcon>🔍</TabIcon>
              <TabLabel>Insights</TabLabel>
              <TabIndicator active={activeView === 'insights'} theme={theme} />
            </NavigationTab>
            <NavigationTab
              active={activeView === 'recommendations'}
              onClick={() => handleViewChange('recommendations')}
              theme={theme}
            >
              <TabIcon>🎯</TabIcon>
              <TabLabel>Recommendations</TabLabel>
              <TabIndicator active={activeView === 'recommendations'} theme={theme} />
            </NavigationTab>
          </NavigationTabs>
        </NavigationContainer>
      </NavigationSection>

      {/* Content Area with Transition */}
      <ContentArea isTransitioning={isTransitioning}>{renderActiveView()}</ContentArea>

      {/* Enhanced Quick Actions */}
      <QuickActionsSection theme={theme}>
        <QuickActionsContainer>
          <QuickActionsTitle theme={theme}>Quick Actions</QuickActionsTitle>
          <QuickActionsGrid>
            <QuickActionCard
              onClick={() => (window.location.href = '/financial-compass')}
              theme={theme}
            >
              <ActionIconLarge>💰</ActionIconLarge>
              <ActionContent>
                <ActionTitle theme={theme}>Financial Compass</ActionTitle>
                <ActionDescription theme={theme}>Manage your financial journey</ActionDescription>
              </ActionContent>
            </QuickActionCard>
            <QuickActionCard onClick={() => (window.location.href = '/seasons')} theme={theme}>
              <ActionIconLarge>🌱</ActionIconLarge>
              <ActionContent>
                <ActionTitle theme={theme}>Life Journey</ActionTitle>
                <ActionDescription theme={theme}>Explore your seasons of life</ActionDescription>
              </ActionContent>
            </QuickActionCard>
            <QuickActionCard
              onClick={() => (window.location.href = '/data-portability')}
              theme={theme}
            >
              <ActionIconLarge>📤</ActionIconLarge>
              <ActionContent>
                <ActionTitle theme={theme}>Export Data</ActionTitle>
                <ActionDescription theme={theme}>Download your complete profile</ActionDescription>
              </ActionContent>
            </QuickActionCard>
          </QuickActionsGrid>
        </QuickActionsContainer>
      </QuickActionsSection>
    </Container>
  );
};

// Animations
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const pulse = keyframes`
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
`;

// Styled Components
const Container = styled.div`
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.background.default} 0%,
    ${({ theme }) => theme.colors.background.secondary} 100%
  );
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.background.default} 0%,
    ${({ theme }) => theme.colors.background.secondary} 100%
  );
`;

const LoadingSpinner = styled.div`
  width: 48px;
  height: 48px;
  border: 4px solid ${({ theme }) => theme.colors.border.light};
  border-top: 4px solid ${({ theme }) => theme.colors.primary.main};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: 16px;
`;

const LoadingText = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1rem;
  animation: ${pulse} 2s ease-in-out infinite;
`;

const HeroHeader = styled.div`
  position: relative;
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.primary.main} 0%,
    ${({ theme }) => theme.colors.primary.dark} 100%
  );
  color: white;
  padding: 48px 24px;
  overflow: hidden;

  @media (max-width: 768px) {
    padding: 32px 16px;
  }
`;

const HeroBackground = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
`;

const HeroContent = styled.div`
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  animation: ${fadeIn} 0.8s ease-out;
`;

const HeroTitle = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0 0 24px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const BreadcrumbNav = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.9rem;
  opacity: 0.8;
`;

const BreadcrumbItem = styled.span<{ active?: boolean }>`
  color: ${({ active }) => (active ? 'white' : 'rgba(255, 255, 255, 0.7)')};
  font-weight: ${({ active }) => (active ? '600' : '400')};
`;

const BreadcrumbSeparator = styled.span`
  color: rgba(255, 255, 255, 0.5);
`;

const NavigationSection = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  position: sticky;
  top: 0;
  z-index: 100;
`;

const NavigationContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (max-width: 768px) {
    padding: 16px;
    gap: 16px;
  }
`;

const ViewInfo = styled.div`
  text-align: center;
  animation: ${slideIn} 0.5s ease-out;
`;

const ViewTitle = styled.h2`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 8px 0;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const ViewDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1rem;
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 0.9rem;
  }
`;

const NavigationTabs = styled.div`
  display: flex;
  justify-content: center;
  gap: 12px;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  padding: 8px;
  border-radius: 16px;

  @media (max-width: 768px) {
    gap: 6px;
    padding: 6px;
  }
`;

const NavigationTab = styled.button<{ active: boolean }>`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 16px 24px;
  border: none;
  background-color: ${({ active, theme }) =>
    active ? theme.colors.background.paper : 'transparent'};
  color: ${({ active, theme }) =>
    active ? theme.colors.primary.main : theme.colors.text.secondary};
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  box-shadow: ${({ active, theme }) => (active ? theme.shadows.sm : 'none')};

  &:hover {
    background-color: ${({ active, theme }) =>
    active ? theme.colors.background.paper : theme.colors.background.default};
    color: ${({ theme }) => theme.colors.primary.main};
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    padding: 12px 16px;
    min-width: 90px;
  }
`;

const TabIndicator = styled.div<{ active: boolean }>`
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: ${({ active }) => (active ? '24px' : '0px')};
  height: 3px;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.primary.main},
    ${({ theme }) => theme.colors.primary.light}
  );
  border-radius: 2px;
  transition: all 0.3s ease;
`;

const TabIcon = styled.span`
  font-size: 1.25rem;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const TabLabel = styled.span`
  font-size: 0.875rem;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 0.75rem;
  }
`;

const ContentArea = styled.div<{ isTransitioning?: boolean }>`
  flex: 1;
  overflow-y: auto;
  opacity: ${({ isTransitioning }) => (isTransitioning ? 0.7 : 1)};
  transform: ${({ isTransitioning }) => (isTransitioning ? 'translateY(10px)' : 'translateY(0)')};
  transition: all 0.3s ease;
`;

const QuickActionsSection = styled.div`
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.background.paper} 0%,
    ${({ theme }) => theme.colors.background.secondary} 100%
  );
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
  padding: 32px 24px;
  box-shadow: ${({ theme }) => theme.shadows.lg};

  @media (max-width: 768px) {
    padding: 24px 16px;
  }
`;

const QuickActionsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const QuickActionsTitle = styled.h3`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 24px 0;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 1.25rem;
    margin-bottom: 20px;
  }
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;

const QuickActionCard = styled.button`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border: 2px solid ${({ theme }) => theme.colors.border.light};
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  box-shadow: ${({ theme }) => theme.shadows.sm};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background.default};
    border-color: ${({ theme }) => theme.colors.primary.main};
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }

  @media (max-width: 768px) {
    padding: 20px;
    gap: 12px;
  }
`;

const ActionIconLarge = styled.div`
  font-size: 2.5rem;
  flex-shrink: 0;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const ActionContent = styled.div`
  flex: 1;
`;

const ActionTitle = styled.h4`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 4px 0;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const ActionDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;

  @media (max-width: 768px) {
    font-size: 0.85rem;
  }
`;

export default HolisticIntegrationPage;

/**
 * Winter Page Component
 *
 * This component serves as the container for the Winter season stages (Calling, Purpose, and Fulfillment).
 * It provides navigation between stages and tracks progress within the Winter season.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../context/SeasonsOfSelfContext';
import { CallingStage } from '../components/Winter/CallingStage';
import { PurposeStage } from '../components/Winter/PurposeStage';
import { FulfillmentStage } from '../components/Winter/FulfillmentStage';
import SeasonTracker from '../components/common/SeasonTracker';
import Button from '../../../components/ui/Button';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface WinterPageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

/**
 * Winter Page Component
 */
const WinterPage: React.FC<WinterPageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { stages, activeStage, setActiveStage, getSeasonCompletionPercentage } = useSeasonsOfSelf();

  // Get Winter stages
  const winterStages = stages.filter((stage) => stage.season === 'winter');

  // Determine active stage based on URL or context
  const [currentStage, setCurrentStage] = useState<string | null>(null);

  useEffect(() => {
    // Extract stage from URL path
    const pathParts = location.pathname.split('/');
    const stagePath = pathParts[pathParts.length - 1];

    // Find stage by path or use first winter stage
    const stageFromPath = winterStages.find((stage) => stage.path.includes(stagePath));

    if (stageFromPath) {
      setCurrentStage(stageFromPath.id);
      setActiveStage(stageFromPath.id);
    } else if (activeStage && winterStages.some((stage) => stage.id === activeStage)) {
      setCurrentStage(activeStage);
    } else if (winterStages.length > 0) {
      setCurrentStage(winterStages[0].id);
      setActiveStage(winterStages[0].id);
    }
  }, [location.pathname, winterStages, activeStage, setActiveStage]);

  // Calculate completion percentage
  const completionPercentage = getSeasonCompletionPercentage('winter');

  // Handle stage navigation
  const handleStageChange = (stageId: string) => {
    setCurrentStage(stageId);
    setActiveStage(stageId);

    // Update URL without full navigation
    const stage = stages.find((s) => s.id === stageId);
    if (stage) {
      navigate(stage.path, { replace: true });
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate('/seasons/autumn');
    }
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    } else {
      navigate('/seasons');
    }
  };

  // Render the current stage component
  const renderStageComponent = () => {
    switch (currentStage) {
      case 'calling':
        return <CallingStage onComplete={() => handleStageChange('purpose')} />;
      case 'purpose':
        return (
          <PurposeStage
            onComplete={() => handleStageChange('fulfillment')}
            onBack={() => handleStageChange('calling')}
          />
        );
      case 'fulfillment':
        return (
          <FulfillmentStage
            onComplete={handleComplete}
            onBack={() => handleStageChange('purpose')}
          />
        );
      default:
        return <div>Select a stage to begin</div>;
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <Header as={motion.div} variants={itemVariants}>
        <Title>Winter: Wisdom</Title>
        <Description>
          The Winter season represents a time of wisdom and purpose in your life journey, focusing
          on discovering your calling, aligning with your purpose, and creating a fulfilling life.
        </Description>
      </Header>

      <motion.div variants={itemVariants}>
        <SeasonTracker season="winter" onStageSelect={handleStageChange} />
      </motion.div>

      <StageContent as={motion.div} variants={itemVariants}>
        {renderStageComponent()}
      </StageContent>

      <Navigation as={motion.div} variants={itemVariants}>
        <Button variant="outlined" onClick={handleBack}>
          Back to Autumn Season
        </Button>
        {completionPercentage === 100 && (
          <Button variant="primary" onClick={handleComplete}>
            Complete Life Journey
          </Button>
        )}
      </Navigation>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
`;

const Header = styled.div`
  margin-bottom: 24px;
  text-align: center;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors.primary.main};
  margin-bottom: 8px;
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  max-width: 800px;
  margin: 0 auto;
`;

const StageContent = styled.div`
  margin-bottom: 24px;
`;

const Navigation = styled.div`
  display: flex;
  justify-content: space-between;
  max-width: 800px;
  margin: 0 auto;
`;

export default WinterPage;

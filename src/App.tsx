/**
 * Main App Component
 *
 * This is the root component of the LifeCompass application.
 * Enhanced with improved theme provider and premium UI/UX.
 */

import React from 'react';
import styled from 'styled-components';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import AppContext from './context/AppContext';
import HomePage from './pages/HomePage';
import { SimpleThemeProvider, useTheme } from './theme/SimpleThemeProvider';
import SimpleGlobalStyles from './styles/SimpleGlobalStyles';
import CompassLayout from './components/layout/CompassLayout';
import HeaderIcons from './components/ui/HeaderIcons';

// Import Direction Pages
import NorthDirectionPage from './features/FinancialCompass/pages/NorthDirectionPage';
import EastDirectionPage from './features/FinancialCompass/pages/EastDirectionPage';
import SouthDirectionPage from './features/FinancialCompass/pages/SouthDirectionPage';
import WestDirectionPage from './features/FinancialCompass/pages/WestDirectionPage';
import FinancialCompassSummaryPage from './features/FinancialCompass/pages/FinancialCompassSummaryPage';

// Import Seasons Pages
import {
  SpringPage,
  SummerPage,
  AutumnPage,
  WinterPage,
  SeasonsOfSelfPage,
} from './features/SeasonsOfSelf';

// Import Integration Pages
import HolisticIntegrationPage from './features/SeasonsOfSelf/pages/HolisticIntegrationPage';

/**
 * App Container
 */
const AppContainer = styled(motion.div)`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.background.default};
  color: ${({ theme }) => theme.colors.text.primary};
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
`;

/**
 * Main App Component
 *
 * This is the root component of the LifeCompass application.
 */
const App: React.FC = () => {
  return (
    <Router>
      <SimpleThemeProvider>
        <SimpleGlobalStyles />
        <AppContext.Provider>
          <AppContent />
        </AppContext.Provider>
      </SimpleThemeProvider>
    </Router>
  );
};

/**
 * App Content Component
 *
 * This component renders the main content of the application.
 * It uses the theme context to apply the current theme.
 */
const AppContent: React.FC = () => {
  const { theme } = useTheme();

  return (
    <AppContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      data-theme={theme.mode}
      data-season={theme.season}
    >
      <HeaderIcons />
      <AnimatePresence mode="wait">
        <Routes>
          <Route path="/" element={<HomePage />} />

          {/* Financial Compass Routes */}
          <Route
            path="/compass"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/summary"
            element={
              <CompassLayout>
                <FinancialCompassSummaryPage />
              </CompassLayout>
            }
          />

          {/* North Direction Routes */}
          <Route
            path="/compass/north"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/north/personal-information"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/north/family-information"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/north/income-details"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/north/expense-details"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/north/cash-flow-analysis"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/north/assets"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/north/liabilities"
            element={
              <CompassLayout>
                <NorthDirectionPage />
              </CompassLayout>
            }
          />

          {/* East Direction Routes */}
          <Route
            path="/compass/east"
            element={
              <CompassLayout>
                <EastDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/east/retirement-goals"
            element={
              <CompassLayout>
                <EastDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/east/retirement-income"
            element={
              <CompassLayout>
                <EastDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/east/retirement-expenses"
            element={
              <CompassLayout>
                <EastDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/east/retirement-timeline"
            element={
              <CompassLayout>
                <EastDirectionPage />
              </CompassLayout>
            }
          />

          {/* South Direction Routes */}
          <Route
            path="/compass/south"
            element={
              <CompassLayout>
                <SouthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/south/insurance-coverage"
            element={
              <CompassLayout>
                <SouthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/south/healthcare-planning"
            element={
              <CompassLayout>
                <SouthDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/south/risk-tolerance"
            element={
              <CompassLayout>
                <SouthDirectionPage />
              </CompassLayout>
            }
          />

          {/* West Direction Routes */}
          <Route
            path="/compass/west"
            element={
              <CompassLayout>
                <WestDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/west/tax-planning"
            element={
              <CompassLayout>
                <WestDirectionPage />
              </CompassLayout>
            }
          />
          <Route
            path="/compass/west/estate-planning"
            element={
              <CompassLayout>
                <WestDirectionPage />
              </CompassLayout>
            }
          />

          {/* Integration Dashboard Route */}
          <Route
            path="/integration"
            element={
              <CompassLayout>
                <HolisticIntegrationPage />
              </CompassLayout>
            }
          />

          {/* Seasons Routes */}
          <Route
            path="/seasons"
            element={
              <CompassLayout>
                <SeasonsOfSelfPage />
              </CompassLayout>
            }
          />

          {/* Spring Season Routes */}
          <Route
            path="/seasons/spring"
            element={
              <CompassLayout>
                <SpringPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/spring/pleasure"
            element={
              <CompassLayout>
                <SpringPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/spring/happiness"
            element={
              <CompassLayout>
                <SpringPage />
              </CompassLayout>
            }
          />

          {/* Summer Season Routes */}
          <Route
            path="/seasons/summer"
            element={
              <CompassLayout>
                <SummerPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/summer/joy"
            element={
              <CompassLayout>
                <SummerPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/summer/momentum"
            element={
              <CompassLayout>
                <SummerPage />
              </CompassLayout>
            }
          />

          {/* Autumn Season Routes */}
          <Route
            path="/seasons/autumn"
            element={
              <CompassLayout>
                <AutumnPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/autumn/pivot"
            element={
              <CompassLayout>
                <AutumnPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/autumn/goal-seeking"
            element={
              <CompassLayout>
                <AutumnPage />
              </CompassLayout>
            }
          />

          {/* Winter Season Routes */}
          <Route
            path="/seasons/winter"
            element={
              <CompassLayout>
                <WinterPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/winter/calling"
            element={
              <CompassLayout>
                <WinterPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/winter/purpose"
            element={
              <CompassLayout>
                <WinterPage />
              </CompassLayout>
            }
          />
          <Route
            path="/seasons/winter/fulfillment"
            element={
              <CompassLayout>
                <WinterPage />
              </CompassLayout>
            }
          />

          {/* Catch-all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </AnimatePresence>
    </AppContainer>
  );
};

export default App;

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LifeCompass - Nature-Inspired Life Navigation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d5016 0%, #1a4d2e 25%, #0f3460 75%, #1e3a8a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .pitch-container {
            max-width: 1200px;
            width: 95%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #90EE90, #32CD32, #228B22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tagline {
            font-size: 1.4rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            font-style: italic;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .value-prop {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #90EE90;
        }

        .value-prop h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #90EE90;
        }

        .value-prop p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: block;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 8px;
            color: #90EE90;
        }

        .feature-desc {
            font-size: 0.95rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .compass-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
        }

        .compass {
            width: 280px;
            height: 280px;
            border: 4px solid #90EE90;
            border-radius: 50%;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
        }

        .compass-direction {
            position: absolute;
            font-weight: bold;
            font-size: 0.9rem;
            color: #90EE90;
            text-align: center;
            line-height: 1.2;
        }

        .north {
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
        }

        .east {
            right: -35px;
            top: 50%;
            transform: translateY(-50%);
        }

        .south {
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
        }

        .west {
            left: -35px;
            top: 50%;
            transform: translateY(-50%);
        }

        .compass-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: bold;
            color: #90EE90;
        }

        .stats-section {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            text-align: center;
        }

        .stat {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            min-width: 120px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #90EE90;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .cta-section {
            text-align: center;
            margin-top: 40px;
        }

        .cta-button {
            background: linear-gradient(45deg, #90EE90, #32CD32);
            color: #1a4d2e;
            padding: 15px 40px;
            border: none;
            border-radius: 30px;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .cta-button:hover {
            transform: scale(1.05);
        }

        .target-audience {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .stats-section {
                flex-direction: column;
                gap: 15px;
            }

            .logo {
                font-size: 2.5rem;
            }

            .target-audience div[style*="grid-template-columns"] {
                display: block !important;
            }

            .target-audience div[style*="grid-template-columns"]>div {
                margin-bottom: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="pitch-container">
        <div class="header">
            <div class="logo">🧭 LifeCompass</div>
            <div class="tagline">Navigate Life's Seasons with Purpose & Harmony</div>
            <div class="subtitle">The Nature-Inspired Life Journey & Financial Wellness Platform</div>
        </div>

        <div class="main-content">
            <div class="value-prop">
                <h2>⚡ The Crisis</h2>
                <p style="font-size: 1.3rem; font-weight: bold; color: #90EE90; margin-bottom: 20px;">"64% of Americans are more afraid of running out of money than dying, yet most lack a comprehensive life plan."
                </p>
                <p>Millions are trapped between financial anxiety and existential emptiness - using 12+ disconnected
                    apps, spreadsheets, and advisors that treat money and meaning as separate worlds.</p>
                <p style="font-weight: bold; color: #FF6B6B;">The result? Paralysis, poor decisions, and unfulfilled
                    lives.</p>

                <h2 style="margin-top: 25px;">🚀 The Solution</h2>
                <p style="font-weight: bold;">LifeCompass is the world's first nature-inspired platform that unifies
                    financial wellness with life purpose - turning overwhelming complexity into intuitive, seasonal
                    guidance.</p>
            </div>

            <div class="compass-visual">
                <div class="compass">
                    <div class="compass-direction north">NORTH<br><small>Where You Are</small></div>
                    <div class="compass-direction east">EAST<br><small>Where You're Going</small></div>
                    <div class="compass-direction south">SOUTH<br><small>What Could Change</small></div>
                    <div class="compass-direction west">WEST<br><small>Your Legacy</small></div>
                    <div class="compass-center">LC</div>
                </div>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <span class="feature-icon">🧭</span>
                <div class="feature-title">Financial Compass</div>
                <div class="feature-desc">4-direction framework for complete financial clarity and planning</div>
            </div>
            <div class="feature-card">
                <span class="feature-icon">🌱</span>
                <div class="feature-title">Life Journey Seasons</div>
                <div class="feature-desc">Spring to Winter life stages with purpose-driven assessments</div>
            </div>
            <div class="feature-card">
                <span class="feature-icon">🎯</span>
                <div class="feature-title">Holistic Integration</div>
                <div class="feature-desc">Ikigai, Values, Strengths, and Mindfulness frameworks</div>
            </div>
            <div class="feature-card">
                <span class="feature-icon">📊</span>
                <div class="feature-title">Smart Analytics</div>
                <div class="feature-desc">AI-powered insights, trends, and personalized recommendations</div>
            </div>
        </div>

        <div class="stats-section">
            <div class="stat">
                <span class="stat-number">4</span>
                <div class="stat-label">Compass Directions</div>
            </div>
            <div class="stat">
                <span class="stat-number">9</span>
                <div class="stat-label">Life Stages</div>
            </div>
            <div class="stat">
                <span class="stat-number">16+</span>
                <div class="stat-label">Assessment Forms</div>
            </div>
            <div class="stat">
                <span class="stat-number">100%</span>
                <div class="stat-label">Data Accuracy</div>
            </div>
        </div>

        <div class="target-audience">
            <h3 style="color: #90EE90; margin-bottom: 15px;">🎯 Target Market Segments</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 20px;">
                <div
                    style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 10px; border-left: 3px solid #90EE90;">
                    <h4 style="color: #90EE90; margin-bottom: 10px;">🏠 Empty Nesters (50-65)</h4>
                    <p style="font-size: 0.9rem; margin-bottom: 8px;"><strong>Size:</strong> 54M households</p>
                    <p style="font-size: 0.9rem; margin-bottom: 8px;"><strong>Income:</strong> $75K-$150K</p>
                    <p style="font-size: 0.9rem;"><strong>Pain:</strong> "What's my purpose now?" Financial freedom
                        meets existential void.</p>
                </div>
                <div
                    style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 10px; border-left: 3px solid #32CD32;">
                    <h4 style="color: #32CD32; margin-bottom: 10px;">🥪 Sandwich Generation (45-55)</h4>
                    <p style="font-size: 0.9rem; margin-bottom: 8px;"><strong>Size:</strong> 47M adults</p>
                    <p style="font-size: 0.9rem; margin-bottom: 8px;"><strong>Income:</strong> $60K-$120K</p>
                    <p style="font-size: 0.9rem;"><strong>Pain:</strong> Caring for kids + aging parents while planning
                        retirement. Overwhelmed & underprepared.</p>
                </div>
                <div
                    style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 10px; border-left: 3px solid #228B22;">
                    <h4 style="color: #228B22; margin-bottom: 10px;">🌱 Purpose-Driven Pre-Retirees (55-67)</h4>
                    <p style="font-size: 0.9rem; margin-bottom: 8px;"><strong>Size:</strong> 31M professionals</p>
                    <p style="font-size: 0.9rem; margin-bottom: 8px;"><strong>Income:</strong> $80K-$200K</p>
                    <p style="font-size: 0.9rem;"><strong>Pain:</strong> Successful but unfulfilled. Seeking meaningful
                        legacy beyond money.</p>
                </div>
            </div>
        </div>

        <div class="cta-section">
            <a href="#" class="cta-button">🚀 Experience LifeCompass Today</a>
            <p style="margin-top: 15px; opacity: 0.8;">
                <em>"Nature's Wisdom. Digital Power. Life Mastery."</em>
            </p>
        </div>
    </div>
</body>

</html>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LifeCompass - Nature-Inspired Life Navigation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .pitch-container {
            max-width: 1200px;
            width: 95%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tagline {
            font-size: 1.4rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            font-style: italic;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .value-prop {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #FFD700;
        }

        .value-prop h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #FFD700;
        }

        .value-prop p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: block;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 8px;
            color: #FFD700;
        }

        .feature-desc {
            font-size: 0.95rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .compass-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
        }

        .compass {
            width: 280px;
            height: 280px;
            border: 4px solid #FFD700;
            border-radius: 50%;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
        }

        .compass-direction {
            position: absolute;
            font-weight: bold;
            font-size: 0.9rem;
            color: #FFD700;
            text-align: center;
            line-height: 1.2;
        }

        .north {
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
        }

        .east {
            right: -35px;
            top: 50%;
            transform: translateY(-50%);
        }

        .south {
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
        }

        .west {
            left: -35px;
            top: 50%;
            transform: translateY(-50%);
        }

        .compass-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: bold;
            color: #FFD700;
        }

        .stats-section {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            text-align: center;
        }

        .stat {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            min-width: 120px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FFD700;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .cta-section {
            text-align: center;
            margin-top: 40px;
        }

        .cta-button {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #333;
            padding: 15px 40px;
            border: none;
            border-radius: 30px;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .cta-button:hover {
            transform: scale(1.05);
        }

        .target-audience {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .stats-section {
                flex-direction: column;
                gap: 15px;
            }

            .logo {
                font-size: 2.5rem;
            }
        }
    </style>
</head>

<body>
    <div class="pitch-container">
        <div class="header">
            <div class="logo">🧭 LifeCompass</div>
            <div class="tagline">Navigate Life's Seasons with Purpose & Harmony</div>
            <div class="subtitle">The Nature-Inspired Life Journey & Financial Wellness Platform</div>
        </div>

        <div class="main-content">
            <div class="value-prop">
                <h2>⚡ The Crisis</h2>
                <p style="font-size: 1.3rem; font-weight: bold; color: #FFD700; margin-bottom: 20px;">"73% of Americans
                    feel financially unprepared for retirement, while 68% report feeling lost about their life purpose."
                </p>
                <p>Millions are trapped between financial anxiety and existential emptiness - using 12+ disconnected
                    apps, spreadsheets, and advisors that treat money and meaning as separate worlds.</p>
                <p style="font-weight: bold; color: #FF6B6B;">The result? Paralysis, poor decisions, and unfulfilled
                    lives.</p>

                <h2 style="margin-top: 25px;">🚀 The Solution</h2>
                <p style="font-weight: bold;">LifeCompass is the world's first nature-inspired platform that unifies
                    financial wellness with life purpose - turning overwhelming complexity into intuitive, seasonal
                    guidance.</p>
            </div>

            <div class="compass-visual">
                <div class="compass">
                    <div class="compass-direction north">NORTH<br><small>Where You Are</small></div>
                    <div class="compass-direction east">EAST<br><small>Where You're Going</small></div>
                    <div class="compass-direction south">SOUTH<br><small>What Could Change</small></div>
                    <div class="compass-direction west">WEST<br><small>Your Legacy</small></div>
                    <div class="compass-center">LC</div>
                </div>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <span class="feature-icon">🧭</span>
                <div class="feature-title">Financial Compass</div>
                <div class="feature-desc">4-direction framework for complete financial clarity and planning</div>
            </div>
            <div class="feature-card">
                <span class="feature-icon">🌱</span>
                <div class="feature-title">Life Journey Seasons</div>
                <div class="feature-desc">Spring to Winter life stages with purpose-driven assessments</div>
            </div>
            <div class="feature-card">
                <span class="feature-icon">🎯</span>
                <div class="feature-title">Holistic Integration</div>
                <div class="feature-desc">Ikigai, Values, Strengths, and Mindfulness frameworks</div>
            </div>
            <div class="feature-card">
                <span class="feature-icon">📊</span>
                <div class="feature-title">Smart Analytics</div>
                <div class="feature-desc">AI-powered insights, trends, and personalized recommendations</div>
            </div>
        </div>

        <div class="stats-section">
            <div class="stat">
                <span class="stat-number">4</span>
                <div class="stat-label">Compass Directions</div>
            </div>
            <div class="stat">
                <span class="stat-number">9</span>
                <div class="stat-label">Life Stages</div>
            </div>
            <div class="stat">
                <span class="stat-number">16+</span>
                <div class="stat-label">Assessment Forms</div>
            </div>
            <div class="stat">
                <span class="stat-number">100%</span>
                <div class="stat-label">Data Accuracy</div>
            </div>
        </div>

        <div class="target-audience">
            <h3 style="color: #FFD700; margin-bottom: 15px;">🎯 Target Market</h3>
            <p><strong>Primary:</strong> Adults 45-75 in midlife transitions, retirement planning, or legacy reflection
            </p>
            <p><strong>Secondary:</strong> Conscious professionals seeking meaning, simplicity, and financial clarity
            </p>
        </div>

        <div class="cta-section">
            <a href="#" class="cta-button">🚀 Experience LifeCompass Today</a>
            <p style="margin-top: 15px; opacity: 0.8;">
                <em>"Where ancient wisdom meets modern technology for holistic life navigation"</em>
            </p>
        </div>
    </div>
</body>

</html>